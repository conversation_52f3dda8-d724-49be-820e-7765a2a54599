@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 208 26% 95%; /* Light Gray #F0F4F7 */
    --foreground: 215 25% 27%; /* Dark Slate Gray #3A4454 */

    --card: 208 26% 99%; /* Slightly off-white */
    --card-foreground: 215 25% 27%; /* Dark Slate Gray */

    --popover: 208 26% 99%; /* Slightly off-white */
    --popover-foreground: 215 25% 27%; /* Dark Slate Gray */

    --primary: 205 66% 63%; /* Calm Blue #5DADE2 */
    --primary-foreground: 210 40% 98%; /* Light Gray/White */

    --secondary: 206 50% 85%; /* Lighter shade of primary blue */
    --secondary-foreground: 205 40% 30%; /* Darker blue for text on secondary */

    --muted: 208 20% 88%; /* Muted Gray */
    --muted-foreground: 210 15% 50%; /* Darker muted gray for text */

    --accent: 166 44% 74%; /* Soft Green #A2D9CE */
    --accent-foreground: 165 30% 30%; /* Darker Green/Gray #356A5F */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 208 20% 80%; /* Slightly darker border than muted */
    --input: 208 20% 85%; /* Similar to border or secondary */
    --ring: 205 66% 55%; /* Slightly darker/more saturated primary for focus rings */

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Sidebar specific colors, can be adjusted if sidebar is used */
    --sidebar-background: 208 26% 92%; /* Slightly darker than main background */
    --sidebar-foreground: 215 25% 22%;
    --sidebar-primary: 205 66% 58%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 166 44% 70%;
    --sidebar-accent-foreground: 165 30% 25%;
    --sidebar-border: 208 20% 75%;
    --sidebar-ring: 205 66% 50%;
  }

  .dark {
    /* Dark theme can be defined here if needed, for now, focusing on light theme */
    --background: 215 25% 15%; /* Dark Slate Blue */
    --foreground: 208 26% 90%; /* Light Grayish Blue */

    --card: 215 25% 20%;
    --card-foreground: 208 26% 90%;

    --popover: 215 25% 20%;
    --popover-foreground: 208 26% 90%;

    --primary: 205 66% 63%; /* Calm Blue */
    --primary-foreground: 215 25% 10%; /* Very Dark Blue */

    --secondary: 206 50% 35%; /* Darker shade of primary blue */
    --secondary-foreground: 208 26% 85%; 

    --muted: 215 20% 30%; 
    --muted-foreground: 210 15% 65%;

    --accent: 166 44% 60%; /* Darker Soft Green */
    --accent-foreground: 165 30% 90%; 

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 215 20% 35%;
    --input: 215 20% 38%;
    --ring: 205 66% 55%;

    --chart-1: 205 70% 50%;
    --chart-2: 166 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 215 25% 12%;
    --sidebar-foreground: 208 26% 88%;
    --sidebar-primary: 205 66% 58%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 166 44% 55%;
    --sidebar-accent-foreground: 165 30% 88%;
    --sidebar-border: 215 20% 28%;
    --sidebar-ring: 205 66% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
