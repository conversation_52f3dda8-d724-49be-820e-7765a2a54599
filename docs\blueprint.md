# **App Name**: GPA Calculator Pro

## Core Features:

- Course Input: Allow users to input course details including subject name, credits, and grade received.
- GPA Calculation: Dynamically display calculated GPA, factoring in course credits and grades.  Support display in multiple GPA scales.
- Data Persistence: Store entered course data in a JSON format, with the capability to locally save and load data from a file.
- Goal Setting: Offer options for users to set target GPA and view progress toward the goal.

## Style Guidelines:

- Primary color: Calm blue (#5DADE2) to evoke trust and focus, reflecting the precision of calculations.
- Background color: Light gray (#F0F4F7), offering a clean and distraction-free backdrop.
- Accent color: Soft green (#A2D9CE) to highlight key actions like 'Calculate' and 'Save', symbolizing academic achievement.
- Clean and modern typography that prioritizes legibility, aiding the user's understanding of presented data and calculations.
- Intuitive and user-friendly interface for easy data input and result interpretation.
- Simple icons to visually represent different subjects or features within the app.